// Communication preferences for profile setup
export type CommunicationType = 'text' | 'audio' | 'video';

// Availability options for profile setup
export type AvailabilityType = 'weekends' | 'weekdays' | 'mornings' | 'afternoons' | 'evenings';

// Learning style options
export type LearningStyleType = 'Visual' | 'Kinesthetic' | 'Auditory' | 'Reading/Writing';

// Teaching approach options
export type TeachingApproachType = 'Hands-on Practice' | 'Project Based' | 'Lecture Style' | 'Discussion Base';

// Session frequency options
export type SessionFrequencyType = 'Weekly' | 'Every two weeks' | 'Monthly' | 'As Needed';

// Session duration options
export type SessionDurationType = '30 minutes' | '45 minutes' | '1 hour' | '1.5 hours' | '2 hours';

// Profile setup form data interface
export interface ProfileSetupData {
    roleId: number;
    fullName: string;
    bio: string;
    goals: string;
    experience: string;
    skills: string;
    communication: CommunicationType[];
    availability: AvailabilityType[];
}

// Privacy settings interface
export interface PrivacySettings {
    makeProfilePrivate: boolean;
    allowMessages: boolean;
    receiveEmailNotifications: boolean;
}

// Preferences form data interface
export interface PreferencesData {
    topics: string[];
    learningStyles: LearningStyleType[];
    teachingApproaches: TeachingApproachType[];
    sessionFrequency: SessionFrequencyType;
    sessionDuration: SessionDurationType;
    privacySettings: PrivacySettings;
}

// Complete registration form data interface
export interface RegistrationFormData {
    step1: {
        email: string;
        password: string;
    };
    step2: ProfileSetupData;
    step3: PreferencesData;
}

// Test data structure interfaces
export interface TestDataPreferences {
    topics: string[];
    learningStyles: LearningStyleType[];
    teachingApproaches: TeachingApproachType[];
    sessionFrequency: SessionFrequencyType;
    sessionDuration: SessionDurationType;
    privacySettings: PrivacySettings;
}

export interface TestDataOptions {
    learningStyles: LearningStyleType[];
    teachingApproaches: TeachingApproachType[];
    sessionFrequencies: SessionFrequencyType[];
    sessionDurations: SessionDurationType[];
}

export interface Step3PreferencesTestData {
    preferenceData: {
        complete: TestDataPreferences;
        options: TestDataOptions;
    };
}
