import { test, expect } from '@playwright/test';
import RegisterAPI from '../../../api/register/register.api';
import { mapToRegisterRequest } from '../../../data-type/register-user-mapper.type';
import apiTestData from '../../../tests-data/user-register-api-data.json';

test.describe('User Registration API Tests', () => {

  test('successful user registration', async ({ request }) => {
    let registerAPI: RegisterAPI;
    let testData: any;
    let response: any;
    let responseText: string;

    await test.step('Arrange test data and API instance', async () => {
      registerAPI = new RegisterAPI(request);
      testData = mapToRegisterRequest(apiTestData.successRegister);
    });

    await test.step('Send registration request', async () => {
      response = await registerAPI.register(testData);
      responseText = await response.text();
    });

    await test.step('Verify successful registration', async () => {
      expect(response.ok()).toBeTruthy();
      expect(response.status()).toBe(200);
    });
  });

  test('unsuccessful registration - email already registered', async ({ request }) => {
    let registerAPI: RegisterAPI;
    let testData: any;
    let response: any;
    let responseText: string;

    await test.step('Arrange test data with duplicate email', async () => {
      registerAPI = new RegisterAPI(request);
      testData = mapToRegisterRequest(apiTestData.unsuccessRegister[0]);
    });

    await test.step('Send registration request with duplicate email', async () => {
      response = await registerAPI.register(testData);
      responseText = await response.text();
    });

    await test.step('Verify duplicate email error', async () => {
      expect(response.ok()).toBeFalsy();
      expect(response.status()).toBe(400);
      expect(responseText).toBe('Email already register.');
    });
  });

  test('unsuccessful registration - invalid expertise', async ({ request }) => {
    let registerAPI: RegisterAPI;
    let testData: any;
    let response: any;
    let responseText: string;

    await test.step('Arrange test data with invalid expertise', async () => {
      registerAPI = new RegisterAPI(request);
      testData = mapToRegisterRequest(apiTestData.unsuccessRegister[1]);
    });

    await test.step('Send registration request with invalid expertise', async () => {
      response = await registerAPI.register(testData);
      responseText = await response.text();
    });

    await test.step('Verify invalid expertise error', async () => {
      expect(response.ok()).toBeFalsy();
      expect(response.status()).toBe(400);
      expect(responseText).toBe('Areas of expertise have invalid value.');
    });
  });

  test('unsuccessful registration - invalid category', async ({ request }) => {
    let registerAPI: RegisterAPI;
    let testData: any;
    let response: any;
    let responseText: string;

    await test.step('Arrange test data with invalid category', async () => {
      registerAPI = new RegisterAPI(request);
      testData = mapToRegisterRequest(apiTestData.unsuccessRegister[2]);
    });

    await test.step('Send registration request with invalid category', async () => {
      response = await registerAPI.register(testData);
      responseText = await response.text();
    });

    await test.step('Verify invalid category error', async () => {
      expect(response.ok()).toBeFalsy();
      expect(response.status()).toBe(400);
      expect(responseText).toBe('Topics have invalid value.');
    });
  });
});