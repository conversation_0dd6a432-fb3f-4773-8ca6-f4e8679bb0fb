import { test, expect } from '@playwright/test';
import { HomePage } from '../../../pages/register/home-page';
import { LoginPage } from '../../../pages/login/login-page';
import { UserManagementPage } from '../../../pages/user-management/user-management-page';
import testData from '../../../tests-data/role-selection-data.json';
import { RoleSelectionTestData } from '../../../data-type/role-selection.type';

test.describe('Role Selection Tests', () => {
    let homePage: HomePage;
    let loginPage: LoginPage;
    let userManagementPage: UserManagementPage;
    const roleData: RoleSelectionTestData = testData as RoleSelectionTestData;

    test.beforeEach(async ({ page }) => {
        homePage = new HomePage(page);
        loginPage = new LoginPage(page);
        userManagementPage = new UserManagementPage(page);
    });

    test('@RoleSelection Verify role selection functionality for Admin role', async ({ page }) => {
        await test.step('Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Login with admin credentials', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                roleData.adminCredentials.email,
                roleData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyLoginSuccessfully();
        });

        await test.step('Navigate to Manage Users page', async () => {
            await userManagementPage.clickManageUsersLink();
            await userManagementPage.verifyOnUserManagementPage();
        });

        await test.step('Select Admin role filter', async () => {
            await userManagementPage.clickSelectRolesButton();
            await userManagementPage.selectRoleOption('Admin');
            await userManagementPage.waitForFilteredResults();
        });

        await test.step('Verify all users have Admin role', async () => {
            await userManagementPage.clickRoleColumnHeader();

            // Click all role cells in the table until end of table
            await userManagementPage.clickAllTableRoleCells();

            // Verify all users have Admin role
            await userManagementPage.verifyAllTableRowsHaveRole('Admin');
        });
    });

    // Data-driven test for multiple role selection scenarios
    roleData.roleSelectionScenarios.forEach((scenario, index) => {
        test(`@RoleSelection Case ${index + 1}: Filter users by "${scenario.roleName}" role`, async ({ page }) => {
            await test.step('Go to home page', async () => {
                await loginPage.goToBrowser();
            });

            await test.step('Go to login page', async () => {
                await loginPage.clickOnSignInLink();
            });

            await test.step('Login with admin credentials', async () => {
                await loginPage.enterEmailAndPasswordToTextBox(
                    roleData.adminCredentials.email,
                    roleData.adminCredentials.password
                );
                await loginPage.clickOnSignInButton();
                await loginPage.verifyLoginSuccessfully();
            });

            await test.step('Navigate to Manage Users page', async () => {
                await userManagementPage.clickManageUsersLink();
                await userManagementPage.verifyOnUserManagementPage();
            });

            await test.step(`Select ${scenario.roleName} role filter`, async () => {
                await userManagementPage.clickSelectRolesButton();
                await userManagementPage.selectRoleOption(scenario.roleName);
                await userManagementPage.waitForFilteredResults();
            });

            await test.step(`Verify filtered results show ${scenario.roleName} users`, async () => {
                await userManagementPage.clickRoleColumnHeader();

                // Click all role cells in the table until end of table
                await userManagementPage.clickAllTableRoleCells();

                // Verify all users in the filtered results have the expected role
                await userManagementPage.verifyAllTableRowsHaveRole(scenario.expectedRoleValue);
            });
        });
    });

    test('@RoleSelection Comprehensive role testing for Admin, Mentor, and Learner', async ({ page }) => {
        await test.step('Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Login with admin credentials', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                roleData.adminCredentials.email,
                roleData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyLoginSuccessfully();
        });

        await test.step('Navigate to Manage Users page', async () => {
            await userManagementPage.clickManageUsersLink();
            await userManagementPage.verifyOnUserManagementPage();
        });

        // Test Admin role
        await test.step('Test Admin role selection and verification', async () => {
            await userManagementPage.clickSelectRolesButton();
            await userManagementPage.selectRoleOption('Admin');
            await userManagementPage.waitForFilteredResults();

            await userManagementPage.clickRoleColumnHeader();
            await userManagementPage.clickAllTableRoleCells();
            await userManagementPage.verifyAllTableRowsHaveRole('Admin');
        });

        // Refresh page to clear filters before testing Mentor role
        await test.step('Refresh page and navigate back to user management', async () => {
            await userManagementPage.refreshPage();
            await userManagementPage.navigateToUserManagement();
        });

        // Test Mentor role
        await test.step('Test Mentor role selection and verification', async () => {
            await userManagementPage.clickSelectRolesButton();
            await userManagementPage.selectRoleOption('Mentor');
            await userManagementPage.waitForFilteredResults();

            await userManagementPage.clickRoleColumnHeader();
            await userManagementPage.clickAllTableRoleCells();
            await userManagementPage.verifyAllTableRowsHaveRole('Mentor');
        });

        // Refresh page to clear filters before testing Learner role
        await test.step('Refresh page and navigate back to user management', async () => {
            await userManagementPage.refreshPage();
            await userManagementPage.navigateToUserManagement();
        });

        // Test Learner role
        await test.step('Test Learner role selection and verification', async () => {
            await userManagementPage.clickSelectRolesButton();
            await userManagementPage.selectRoleOption('Learner');
            await userManagementPage.waitForFilteredResults();

            await userManagementPage.clickRoleColumnHeader();
            await userManagementPage.clickAllTableRoleCells();
            await userManagementPage.verifyAllTableRowsHaveRole('Learner');
        });
    });

    // Individual tests for each role to avoid filter conflicts
    test('@RoleSelection Individual test - Admin role only', async ({ page }) => {
        await test.step('Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Login with admin credentials', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                roleData.adminCredentials.email,
                roleData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyLoginSuccessfully();
        });

        await test.step('Navigate to Manage Users page', async () => {
            await userManagementPage.clickManageUsersLink();
            await userManagementPage.verifyOnUserManagementPage();
        });

        await test.step('Test Admin role selection and verification', async () => {
            await userManagementPage.clickSelectRolesButton();
            await userManagementPage.selectRoleOption('Admin');
            await userManagementPage.waitForFilteredResults();

            await userManagementPage.clickRoleColumnHeader();
            await userManagementPage.clickAllTableRoleCells();
            await userManagementPage.verifyAllTableRowsHaveRole('Admin');
        });
    });

    test('@RoleSelection Individual test - Mentor role only', async ({ page }) => {
        await test.step('Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Login with admin credentials', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                roleData.adminCredentials.email,
                roleData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyLoginSuccessfully();
        });

        await test.step('Navigate to Manage Users page', async () => {
            await userManagementPage.clickManageUsersLink();
            await userManagementPage.verifyOnUserManagementPage();
        });

        await test.step('Test Mentor role selection and verification', async () => {
            await userManagementPage.clickSelectRolesButton();
            await userManagementPage.selectRoleOption('Mentor');
            await userManagementPage.waitForFilteredResults();

            await userManagementPage.clickRoleColumnHeader();
            await userManagementPage.clickAllTableRoleCells();
            await userManagementPage.verifyAllTableRowsHaveRole('Mentor');
        });
    });

    test('@RoleSelection Individual test - Learner role only', async ({ page }) => {
        await test.step('Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Login with admin credentials', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                roleData.adminCredentials.email,
                roleData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyLoginSuccessfully();
        });

        await test.step('Navigate to Manage Users page', async () => {
            await userManagementPage.clickManageUsersLink();
            await userManagementPage.verifyOnUserManagementPage();
        });

        await test.step('Test Learner role selection and verification', async () => {
            await userManagementPage.clickSelectRolesButton();
            await userManagementPage.selectRoleOption('Learner');
            await userManagementPage.waitForFilteredResults();

            await userManagementPage.clickRoleColumnHeader();
            await userManagementPage.clickAllTableRoleCells();
            await userManagementPage.verifyAllTableRowsHaveRole('Learner');
        });
    });
});
