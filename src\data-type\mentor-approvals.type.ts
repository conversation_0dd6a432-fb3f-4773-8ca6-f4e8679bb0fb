export interface MentorSearchData {
    searchTerm: string;
    expectedResults: {
        fullName: string;
        shouldBeVisible: boolean;
    }[];
}

export interface AdminCredentials {
    email: string;
    password: string;
}

export interface MentorDetailData {
    fullName: string;
    hasApplicantInformation: boolean;
    hasApplicationDecision: boolean;
    hasEducation: boolean;
    hasWorkExperience: boolean;
    hasCertifications: boolean;
    hasMotivation: boolean;
    hasSupportingDocuments: boolean;
}

export interface MentorReviewTestData {
    searchTerm: string;
    mentorDetails: MentorDetailData;
}

export interface DecisionMakingData {
    requestUpdateReason: string;
    rejectionReason: string;
    shouldTestApproval: boolean;
    shouldTestRejection: boolean;
    shouldTestRequestUpdate: boolean;
}

export interface MentorDecisionTestData {
    searchTerm: string;
    mentorDetails: MentorDetailData;
    decisionActions: DecisionMakingData;
}

export interface MentorApprovalsTestData {
    adminCredentials: AdminCredentials;
    searchScenarios: MentorSearchData[];
    reviewScenarios?: MentorReviewTestData[];
    decisionScenarios?: MentorDecisionTestData[];
}
