import { test } from "@core/fixture/fixture-api";
import User<PERSON><PERSON> from "src/api/user/user.api";
import { expect } from "@playwright/test";
import testData from "@tests-data/user-role-management-data.json";

test.describe('User Role Management API', () => {
    let userAPI: UserAPI

    test.beforeEach(async ({ request }) => {
        userAPI = new UserAPI(request);
    });

    test("Verify that all the information of all users are fetched successfully", async () => {
        let res: any;
        let resBody: any;

        await test.step('Send GET request to fetch all users', async () => {
            res = await userAPI.getAllUsers();
            resBody = await res.json();
        });

        await test.step('Verify successful response', async () => {
            await expect(resBody.statusCode).toBe(200);
        });
    });

    testData.activeUser.forEach((account, index) => {
        test(`${index + 1}: Verify update status of active account of user successfully`, async () => {
            let res: any;
            let resBody: any;

            await test.step('Send PATCH request to activate user', async () => {
                res = await userAPI.updateActiveUserStatus(account.id);
                resBody = await res.json();
            });

            await test.step('Verify successful activation', async () => {
                expect.soft(resBody.statusCode).toBe(200);
            });
        });
    })

    testData.activeUserError.forEach((account, index) => {
        test(`Negative Case ${index + 1}: Verify update status of active account of user`, async () => {
            let res: any;
            let resBody: any;

            await test.step('Send PATCH request with invalid user ID', async () => {
                res = await userAPI.updateActiveUserStatus(account.id);
                resBody = await res.json();
            });

            await test.step('Verify error response', async () => {
                expect.soft(resBody.statusCode).toBe(account.statusCode);
                expect.soft(resBody.errors[0].message).toBe(account.message);
            });
        });
    })

    testData.deactiveUser.forEach((account, index) => {
        test(`Case ${index + 1}: Verify update status of deactive account of user successfully`, async () => {
            let res: any;
            let resBody: any;

            await test.step('Send PATCH request to deactivate user', async () => {
                res = await userAPI.updateDeactiveStatus(account.id);
                resBody = await res.json();
            });

            await test.step('Verify successful deactivation', async () => {
                expect(resBody.statusCode).toBe(200);
            });
        });
    })

    testData.deactiveUserError.forEach((account, index) => {
        test(`Negative Case ${index + 1}: Verify update status of deactive account of user`, async () => {
            let res: any;
            let resBody: any;

            await test.step('Send PATCH request with invalid user ID', async () => {
                res = await userAPI.updateDeactiveStatus(account.id);
                resBody = await res.json();
            });

            await test.step('Verify error response', async () => {
                expect.soft(resBody.statusCode).toBe(account.statusCode);
                expect.soft(resBody.errors[0].message).toBe(account.message);
            });
        });
    })
})