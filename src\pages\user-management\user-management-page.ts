import { Locator, Page, expect } from '@playwright/test';
import { BasePage } from '../base-page';

export class UserManagementPage extends BasePage {
    // Locators
    private readonly manageUsersLink: Locator;
    private readonly searchBox: Locator;
    private readonly nameColumnHeader: Locator;
    private readonly userTable: Locator;

    constructor(page: Page) {
        super(page);
        this.manageUsersLink = this.page.getByRole('link', { name: 'Manage Users' });
        this.searchBox = this.page.getByRole('textbox', { name: 'Search...' });
        this.nameColumnHeader = this.page.getByRole('cell', { name: 'Name' });
        this.userTable = this.page.locator('table, [role="table"]');
    }

    async clickManageUsersLink(): Promise<void> {
        await this.manageUsersLink.click();
    }

    async verifyOnUserManagementPage(): Promise<void> {
        await expect(this.manageUsersLink).toBeVisible();
        await expect(this.page).toHaveURL(/.*user.*management.*|.*manage.*user.*/i);
    }

    async clickSearchBox(): Promise<void> {
        await this.searchBox.click();
    }

    async searchForUser(searchTerm: string): Promise<void> {
        await this.searchBox.fill(searchTerm);
    }

    async clickNameColumnHeader(): Promise<void> {
        await this.nameColumnHeader.click();
    }

    async verifyUserInResults(userName: string): Promise<void> {
        const userNameLocator = this.page.getByText(userName);
        await expect(userNameLocator).toBeVisible();
    }

    async clickOnUserName(userName: string): Promise<void> {
        await this.page.getByText(userName).click();
    }

    async verifySearchResults(expectedUsers: string[]): Promise<void> {
        for (const userName of expectedUsers) {
            await this.verifyUserInResults(userName);
        }
    }

    async verifyNoResults(): Promise<void> {
        // Check if table is empty or shows "No results" message
        const noResultsMessage = this.page.getByText(/no.*results|no.*users.*found|empty/i);
        const emptyTable = this.userTable.locator('tbody tr').count();
        
        try {
            await expect(noResultsMessage).toBeVisible({ timeout: 5000 });
        } catch {
            // If no "no results" message, check if table is empty
            expect(await emptyTable).toBe(0);
        }
    }
}
