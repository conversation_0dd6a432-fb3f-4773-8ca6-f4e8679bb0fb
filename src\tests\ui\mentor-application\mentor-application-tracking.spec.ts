import { test, expect, devices } from '@playwright/test';

import { LoginPage } from '../../../pages/login/login-page';
import { MentorApplicationStatusPage } from '../../../pages/mentor-application/mentor-application-status-page';
import testData from '../../../tests-data/mentor-application-data.json';
import { MentorApplicationTestData, PresubmittedMentorAccount } from '../../../data-type/mentor-application.type';

// Configure test to run only on Chromium
test.use({
    ...devices['Desktop Chrome'],
    // Alternatively, you can use specific browser configuration
    // browserName: 'chromium'
});

test.describe('Mentor Application Tracking Tests', () => {
    let loginPage: LoginPage;
    let mentorApplicationStatusPage: MentorApplicationStatusPage;

    // Type the test data
    const applicationData: MentorApplicationTestData = testData as MentorApplicationTestData;

    test.beforeEach(async ({ page }) => {
        loginPage = new LoginPage(page);
        mentorApplicationStatusPage = new MentorApplicationStatusPage(page);
    });

    test('@MentorApplicationTracking Refresh test - Verify mentor application status refresh functionality', async ({ page }) => {
        const refreshTestMentor: PresubmittedMentorAccount = applicationData.refreshTestMentor;

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
            await expect(page).toHaveURL('/');
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
            await expect(page).toHaveURL(/.*login.*/);
        });

        await test.step('Step 3: Login as presubmitted application mentor', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                refreshTestMentor.email,
                refreshTestMentor.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyMentorLoginSuccessfully();
        });

        await test.step('Step 4: Make sure it\'s on Mentor Application Status page', async () => {
            // Verify we're on the correct page
            await page.getByText('Mentor Application Status').click();
            await mentorApplicationStatusPage.verifyOnMentorApplicationStatusPage();
        });

        await test.step('Step 5: Click refresh button', async () => {
            await page.getByRole('button', { name: 'Refresh' }).click();

            // Wait for any loading/refresh to complete
            await page.waitForLoadState('networkidle');
        });

        await test.step('Check result: Verify fullname and email match account from test-data', async () => {
            // Verify fullname matches - using exact selector as specified
            await page.getByRole('heading', { name: 'mentor123' }).click();

            // Verify email matches - using exact selector as specified
            await page.getByText('<EMAIL>').click();

            // Additional verification using page object methods
            await mentorApplicationStatusPage.verifyMentorApplicationDetails(
                refreshTestMentor.fullName,
                refreshTestMentor.email
            );
        });
    });

    // Data-driven test for multiple presubmitted mentors
    applicationData.presubmittedMentors.forEach((mentor, index) => {
        test(`@MentorApplicationTracking Case ${index + 1}: Verify application status for ${mentor.fullName}`, async ({ page }) => {
            // Initialize page objects for this test
            const loginPageLocal = new LoginPage(page);
            const mentorApplicationStatusPageLocal = new MentorApplicationStatusPage(page);

            await test.step('Step 1: Go to home page', async () => {
                await loginPageLocal.goToBrowser();
            });

            await test.step('Step 2: Go to login page', async () => {
                await loginPageLocal.clickOnSignInLink();
            });

            await test.step('Step 3: Login as presubmitted mentor', async () => {
                await loginPageLocal.enterEmailAndPasswordToTextBox(mentor.email, mentor.password);
                await loginPageLocal.clickOnSignInButton();
                await loginPageLocal.verifyMentorLoginSuccessfully();
            });

            await test.step('Step 4: Navigate to Mentor Application Status page', async () => {
                await page.getByText('Mentor Application Status').click();
                await mentorApplicationStatusPageLocal.verifyOnMentorApplicationStatusPage();
            });

            await test.step('Step 5: Verify mentor application details', async () => {
                await mentorApplicationStatusPageLocal.verifyMentorApplicationDetails(
                    mentor.fullName,
                    mentor.email
                );

                // Verify application status if specified
                if (mentor.expectedStatus) {
                    await mentorApplicationStatusPageLocal.verifyApplicationStatus(mentor.expectedStatus);
                }
            });
        });
    });
});
