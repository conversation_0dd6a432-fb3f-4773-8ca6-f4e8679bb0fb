{"adminCredentials": {"email": "<EMAIL>", "password": "admin123A@"}, "roleSelectionScenarios": [{"roleName": "Admin", "expectedRoleValue": "Admin", "description": "Filter and verify all users with Admin role"}, {"roleName": "Mentor", "expectedRoleValue": "Mentor", "description": "Filter and verify all users with Mentor role"}, {"roleName": "<PERSON><PERSON>", "expectedRoleValue": "<PERSON><PERSON>", "description": "Filter and verify all users with Learner role"}]}