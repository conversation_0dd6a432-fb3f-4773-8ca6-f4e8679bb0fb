import { expect, test } from "@playwright/test";
import LoginLogoutAPI from "src/api/login-logout/login-logout.api";
require('dotenv').config();

test.describe('User Logout API', () => {
    let loginLogoutAPI: LoginLogoutAPI;

    test.beforeEach(async ({ request }) => {
        loginLogoutAPI = new LoginLogoutAPI(request);
    });

    test("Verify successful logout and token invalidation", async () => {
        let loginRes: any;
        let logoutRes: any;
        let postLogoutRes: any;
        let postLogoutBody: any;

        await test.step('Login to get a valid session', async () => {
            loginRes = await loginLogoutAPI.login(process.env.EMAIL, process.env.PASSWORD);
            expect(loginRes.status()).toBe(200);
        });

        await test.step('Attempt logout', async () => {
            logoutRes = await loginLogoutAPI.logout();
            expect(logoutRes.status()).toBe(200);
        });

        await test.step('Verify token invalidation by attempting login again', async () => {
            postLogoutRes = await loginLogoutAPI.login(process.env.EMAIL, process.env.PASSWORD);
            postLogoutBody = await postLogoutRes.json();

            expect(postLogoutRes.status()).toBe(401);
            expect(postLogoutBody.errors[0].message).toBe("Unauthorized");
        });
    });
});