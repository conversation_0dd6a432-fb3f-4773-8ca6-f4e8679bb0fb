import { RegisterUserRequest } from './register-user.type';


export function mapToRegisterRequest(data: any): RegisterUserRequest {
    const expertises = data.expertises ? (Array.isArray(data.expertises) ? data.expertises : [data.expertises]) : [];
    const courseCategoryIds = data.courseCategoryIds ? (Array.isArray(data.courseCategoryIds) ? data.courseCategoryIds : [data.courseCategoryIds]) : [];
    const teachingStyles = data.teachingStyles ? (Array.isArray(data.teachingStyles) ? data.teachingStyles : [data.teachingStyles]) : [];

    return {
        Email: data.email,
        Password: data.password,
        AvatarUrl: data.avatarUrl || undefined,
        FullName: data.fullname,
        Role: parseInt(data.role),
        Bio: data.bio || undefined,
        Expertises: expertises,
        ProfessionalSkill: data.professionalSkill || undefined,
        Experience: data.experience || undefined,
        CommunicationPreference: data.communicationPreference || undefined,
        Goals: data.goals || undefined,
        CourseCategoryIds: courseCategoryIds,
        SessionFrequency: data.sessionFrequency || undefined,
        Duration: data.duration || undefined,
        LearningStyle: data.learningStyle || undefined,
        TeachingStyles: teachingStyles
    };
}
