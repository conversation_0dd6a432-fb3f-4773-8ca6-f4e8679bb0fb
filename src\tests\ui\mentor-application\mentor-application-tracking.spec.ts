import { test, expect } from '@playwright/test';
import { HomePage } from '../../../pages/register/home-page';
import { LoginPage } from '../../../pages/login/login-page';
import { MentorApplicationStatusPage } from '../../../pages/mentor-application/mentor-application-status-page';
import testData from '../../../tests-data/mentor-application-data.json';
import { MentorApplicationTestData, PresubmittedMentorAccount } from '../../../data-type/mentor-application.type';

test.describe('Mentor Application Tracking Tests', () => {
    let homePage: HomePage;
    let loginPage: LoginPage;
    let mentorApplicationStatusPage: MentorApplicationStatusPage;
    
    // Type the test data
    const applicationData: MentorApplicationTestData = testData as MentorApplicationTestData;

    test.beforeEach(async ({ page }) => {
        homePage = new HomePage(page);
        loginPage = new LoginPage(page);
        mentorApplicationStatusPage = new MentorApplicationStatusPage(page);
    });

    test('@MentorApplicationTracking Refresh test - Verify mentor application status refresh functionality', async ({ page }) => {
        const refreshTestMentor: PresubmittedMentorAccount = applicationData.refreshTestMentor;

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
            await expect(page).toHaveURL('/');
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
            await expect(page).toHaveURL(/.*login.*/);
        });

        await test.step('Step 3: Login as presubmitted application mentor', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                refreshTestMentor.email, 
                refreshTestMentor.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyLoginSuccessfully();
        });

        await test.step('Step 4: Navigate to Mentor Application Status page', async () => {
            await mentorApplicationStatusPage.navigateToMentorApplicationStatus();
            await mentorApplicationStatusPage.verifyOnMentorApplicationStatusPage();
            
            // Verify we're on the correct page
            await page.getByText('Mentor Application Status').click();
        });

        await test.step('Step 5: Click refresh button', async () => {
            await page.getByRole('button', { name: 'Refresh' }).click();
            
            // Wait for any loading/refresh to complete
            await page.waitForLoadState('networkidle');
        });

        await test.step('Check result: Verify fullname and email match account from test-data', async () => {
            // Verify fullname matches
            await page.getByRole('heading', { name: refreshTestMentor.fullName }).click();
            
            // Verify email matches  
            await page.getByText(refreshTestMentor.email).click();
            
            // Additional verification using page object methods
            await mentorApplicationStatusPage.verifyMentorApplicationDetails(
                refreshTestMentor.fullName,
                refreshTestMentor.email
            );
        });
    });

    // Data-driven test for multiple presubmitted mentors
    applicationData.presubmittedMentors.forEach((mentor, index) => {
        test(`@MentorApplicationTracking Case ${index + 1}: Verify application status for ${mentor.fullName}`, async ({ page }) => {
            await test.step('Step 1: Go to home page', async () => {
                await loginPage.goToBrowser();
            });

            await test.step('Step 2: Go to login page', async () => {
                await loginPage.clickOnSignInLink();
            });

            await test.step('Step 3: Login as presubmitted mentor', async () => {
                await loginPage.enterEmailAndPasswordToTextBox(mentor.email, mentor.password);
                await loginPage.clickOnSignInButton();
                await loginPage.verifyLoginSuccessfully();
            });

            await test.step('Step 4: Navigate to Mentor Application Status page', async () => {
                await mentorApplicationStatusPage.navigateToMentorApplicationStatus();
                await mentorApplicationStatusPage.verifyOnMentorApplicationStatusPage();
            });

            await test.step('Step 5: Verify mentor application details', async () => {
                await mentorApplicationStatusPage.verifyMentorApplicationDetails(
                    mentor.fullName,
                    mentor.email
                );
                
                // Verify application status if specified
                if (mentor.expectedStatus) {
                    await mentorApplicationStatusPage.verifyApplicationStatus(mentor.expectedStatus);
                }
            });
        });
    });
});
