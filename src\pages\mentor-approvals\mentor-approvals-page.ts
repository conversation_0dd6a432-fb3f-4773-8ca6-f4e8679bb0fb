import { Locator, Page, expect } from '@playwright/test';
import { BasePage } from '../base-page';

export class MentorApprovalsPage extends BasePage {
    // Locators
    private readonly mentorApprovalsLink: Locator;
    private readonly searchBox: Locator;
    private readonly fullNameColumnHeader: Locator;
    private readonly mentorApprovalsHeading: Locator;
    private readonly mentorTable: Locator;

    constructor(page: Page) {
        super(page);
        this.mentorApprovalsLink = this.page.getByRole('link', { name: 'Mentor Approvals' });
        this.searchBox = this.page.getByRole('textbox', { name: 'Search...' });
        this.fullNameColumnHeader = this.page.getByRole('cell', { name: 'Full Name' });
        this.mentorApprovalsHeading = this.page.getByRole('heading', { name: 'Mentor Approvals Management' });
        this.mentorTable = this.page.locator('table, [role="table"]');
    }

    async clickMentorApprovalsLink(): Promise<void> {
        await this.mentorApprovalsLink.click();
    }

    async verifyOnMentorApprovalsPage(): Promise<void> {
        await expect(this.mentorApprovalsHeading).toBeVisible();
        await expect(this.page).toHaveURL(/.*mentor.*approval.*|.*approval.*mentor.*/i);
    }

    async clickMentorApprovalsHeading(): Promise<void> {
        await this.mentorApprovalsHeading.click();
    }

    async clickSearchBox(): Promise<void> {
        await this.searchBox.click();
    }

    async searchForMentor(searchTerm: string): Promise<void> {
        await this.searchBox.fill(searchTerm);
    }

    async clickFullNameColumnHeader(): Promise<void> {
        await this.fullNameColumnHeader.click();
    }

    async verifyMentorInResults(mentorName: string): Promise<void> {
        const mentorNameLocator = this.page.getByText(mentorName);
        await expect(mentorNameLocator).toBeVisible();
    }

    async clickOnMentorName(mentorName: string): Promise<void> {
        await this.page.getByText(mentorName).click();
    }

    async verifySearchResults(expectedMentors: string[]): Promise<void> {
        for (const mentorName of expectedMentors) {
            await this.verifyMentorInResults(mentorName);
        }
    }

    async verifyNoResults(): Promise<void> {
        // Check if table is empty or shows "No results" message
        const noResultsMessage = this.page.getByText(/no.*results|no.*mentors.*found|empty/i);
        const emptyTable = this.mentorTable.locator('tbody tr').count();

        try {
            await expect(noResultsMessage).toBeVisible({ timeout: 5000 });
        } catch {
            // If no "no results" message, check if table is empty
            expect(await emptyTable).toBe(0);
        }
    }

    async clickViewDetailsButton(mentorName: string): Promise<void> {
        const mentorRow = this.page.getByRole('row', { name: mentorName });
        const viewDetailsButton = mentorRow.getByRole('button');
        await viewDetailsButton.click();
    }
}
