import { test, expect } from '@playwright/test';
import { HomePage } from '../../../pages/register/home-page';
import { LoginPage } from '../../../pages/login/login-page';
import { UserManagementPage } from '../../../pages/user-management/user-management-page';
import testData from '../../../tests-data/user-activation-data.json';
import { UserActivationTestData } from '../../../data-type/user-activation.type';

test.describe('User Activation/Deactivation Tests', () => {
    let homePage: HomePage;
    let loginPage: LoginPage;
    let userManagementPage: UserManagementPage;
    const activationData: UserActivationTestData = testData as UserActivationTestData;

    test.beforeEach(async ({ page }) => {
        homePage = new HomePage(page);
        loginPage = new LoginPage(page);
        userManagementPage = new UserManagementPage(page);
    });

    test('@UserActivation Verify user deactivation and activation functionality', async ({ page }) => {
        await test.step('Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Login with admin credentials', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                activationData.adminCredentials.email,
                activationData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyLoginSuccessfully();
        });

        await test.step('Navigate to Manage Users page', async () => {
            await userManagementPage.clickManageUsersLink();
            await userManagementPage.verifyOnUserManagementPage();
        });

        await test.step('Deactivate user at index 3', async () => {
            await userManagementPage.clickDeactivateUserButton(3);
            await userManagementPage.verifyDeactivateDialog();
            await userManagementPage.clickDeactivateConfirmButton();
            await userManagementPage.verifyDeactivationMessage();
        });

        await test.step('Activate user at index 0', async () => {
            await userManagementPage.clickActivateUserButton(0);
            await userManagementPage.verifyActivateDialog();
            await userManagementPage.clickActivateConfirmButton();
            await userManagementPage.verifyActivationMessage();
        });
    });

    // Data-driven test for multiple activation/deactivation scenarios
    activationData.activationScenarios.forEach((scenario, index) => {
        test(`@UserActivation Case ${index + 1}: ${scenario.action} user at index ${scenario.userIndex}`, async ({ page }) => {
            await test.step('Go to home page', async () => {
                await loginPage.goToBrowser();
            });

            await test.step('Go to login page', async () => {
                await loginPage.clickOnSignInLink();
            });

            await test.step('Login with admin credentials', async () => {
                await loginPage.enterEmailAndPasswordToTextBox(
                    activationData.adminCredentials.email,
                    activationData.adminCredentials.password
                );
                await loginPage.clickOnSignInButton();
                await loginPage.verifyLoginSuccessfully();
            });

            await test.step('Navigate to Manage Users page', async () => {
                await userManagementPage.clickManageUsersLink();
                await userManagementPage.verifyOnUserManagementPage();
            });

            await test.step(`${scenario.action} user at index ${scenario.userIndex}`, async () => {
                await userManagementPage.performUserAction(scenario.action, scenario.userIndex);
            });
        });
    });

    test('@UserActivation Detailed step-by-step deactivation and activation', async ({ page }) => {
        await test.step('Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Login with admin credentials', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                activationData.adminCredentials.email,
                activationData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyLoginSuccessfully();
        });

        await test.step('Navigate to Manage Users page', async () => {
            await userManagementPage.clickManageUsersLink();
            await userManagementPage.verifyOnUserManagementPage();
        });

        await test.step('Perform detailed deactivation process', async () => {
            // Click deactivate button for user at index 3
            await userManagementPage.clickDeactivateUserButton(3);
            
            // Verify and interact with deactivate dialog
            await userManagementPage.verifyDeactivateDialog();
            
            // Click confirm deactivate button
            await userManagementPage.clickDeactivateConfirmButton();
            
            // Verify deactivation success message
            await userManagementPage.verifyDeactivationMessage();
        });

        await test.step('Perform detailed activation process', async () => {
            // Click activate button for first user
            await userManagementPage.clickActivateUserButton(0);
            
            // Verify and interact with activate dialog
            await userManagementPage.verifyActivateDialog();
            
            // Click confirm activate button
            await userManagementPage.clickActivateConfirmButton();
            
            // Verify activation success message
            await userManagementPage.verifyActivationMessage();
        });
    });

    test('@UserActivation Test multiple user activations and deactivations', async ({ page }) => {
        await test.step('Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Login with admin credentials', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                activationData.adminCredentials.email,
                activationData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyLoginSuccessfully();
        });

        await test.step('Navigate to Manage Users page', async () => {
            await userManagementPage.clickManageUsersLink();
            await userManagementPage.verifyOnUserManagementPage();
        });

        await test.step('Deactivate multiple users', async () => {
            // Deactivate user at index 3
            await userManagementPage.deactivateUser(3);
            
            // Wait a moment between actions
            await page.waitForTimeout(1000);
            
            // Deactivate user at index 1
            await userManagementPage.deactivateUser(1);
        });

        await test.step('Activate multiple users', async () => {
            // Wait a moment before activating
            await page.waitForTimeout(1000);
            
            // Activate user at index 0
            await userManagementPage.activateUser(0);
            
            // Wait a moment between actions
            await page.waitForTimeout(1000);
            
            // Activate user at index 2
            await userManagementPage.activateUser(2);
        });
    });
});
