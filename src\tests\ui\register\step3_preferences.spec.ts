import { test, expect } from '@playwright/test';
import { HomePage } from '../../../pages/register/home-page';
import { RegisterPage } from '../../../pages/register/register-page';
import testData from '../../../tests-data/user-register-data.json';
import {
  CommunicationType,
  AvailabilityType,
  TestDataPreferences,
  TestDataOptions
} from '../../../data-type/register-ui-form.type';

test.describe('Step 3: Preferences Setup Tests', () => {
  let homePage: HomePage;
  let registerPage: RegisterPage;

  // Generate a unique test ID to avoid data collisions
  const timestamp = Date.now();
  const testId = `test${timestamp}`;

  test.beforeEach(async ({ page }) => {
    homePage = new HomePage(page);
    registerPage = new RegisterPage(page);

    await test.step('Navigate to registration page', async () => {
      await registerPage.NavigateToRegisterPage(homePage);
    });

    await test.step('Complete Step 1 registration', async () => {
      const validData = testData.registration.api.validMentor;
      // Use simple email format without special characters
      const email = `${testId}${validData.Email.replace(/[^a-zA-Z0-9@.]/g, '')}`;
      await registerPage.completeStep1Registration(
        email,
        validData.Password
      );
    });

    await test.step('Complete Step 2 profile setup', async () => {
      const validData = testData.registration.api.validMentor;
      const mentorProfileData = testData.registration.step2_profile_setup.profileData.mentor;

      await registerPage.completeStep2Profile({
        roleId: validData.Role,
        fullName: validData.FullName,
        bio: validData.Bio,
        goals: mentorProfileData.goals,
        experience: mentorProfileData.experience,
        skills: mentorProfileData.skills,
        communication: mentorProfileData.communication as CommunicationType[],
        availability: mentorProfileData.availability as AvailabilityType[]
      });
    });

    await test.step('Verify preferences page is displayed', async () => {
      await expect(page.getByRole('heading', { name: 'Session Preferences' })).toBeVisible();
    });
  });

  test('Complete Step 3 Preferences Setup', async ({ page }) => {
    await test.step('Fill out preferences form', async () => {
      const preferenceData = testData.registration.step3_preferences.preferenceData.complete as TestDataPreferences;
      await registerPage.completeStep3Preferences(preferenceData);
    });

    await test.step('Verify successful registration', async () => {
      await expect(page.getByText('Account created successfully')).toBeVisible();
    });
  });

  test.describe('Learning and Teaching Preferences', () => {
    test('Verify All Learning Styles Options', async () => {
      await test.step('Select each learning style option', async () => {
        const options = testData.registration.step3_preferences.preferenceData.options as TestDataOptions;
        for (const style of options.learningStyles) {
          await registerPage.selectLearningStyle(style);
        }
      });
    });

    test('Verify All Teaching Approaches', async () => {
      await test.step('Select each teaching approach', async () => {
        const options = testData.registration.step3_preferences.preferenceData.options as TestDataOptions;
        for (const approach of options.teachingApproaches) {
          await registerPage.selectTeachingApproach(approach);
        }
      });
    });

    test('Verify Session Frequency Options', async ({ page }) => {
      await test.step('Select each frequency option', async () => {
        const options = testData.registration.step3_preferences.preferenceData.options as TestDataOptions;
        for (const frequency of options.sessionFrequencies) {
          await registerPage.selectSessionFrequency(frequency);
        }
      });

      await test.step('Verify final selection', async () => {
        const options = testData.registration.step3_preferences.preferenceData.options as TestDataOptions;
        const lastFrequency = options.sessionFrequencies[options.sessionFrequencies.length - 1];
        await expect(page.getByRole('combobox').filter({ hasText: lastFrequency })).toBeVisible();
      });
    });

    test('Verify Session Duration Options', async ({ page }) => {
      await test.step('Select each duration option', async () => {
        const options = testData.registration.step3_preferences.preferenceData.options as TestDataOptions;
        for (const duration of options.sessionDurations) {
          await registerPage.selectSessionDuration(duration);
        }
      });

      await test.step('Verify final selection', async () => {
        const options = testData.registration.step3_preferences.preferenceData.options as TestDataOptions;
        const lastDuration = options.sessionDurations[options.sessionDurations.length - 1];
        await expect(page.getByRole('combobox').filter({ hasText: lastDuration })).toBeVisible();
      });
    });
  });

  test.describe('Privacy Settings', () => {
    test('TS-311: Verify default privacy settings', async () => {
      await test.step('Check default settings', async () => {
        const defaults = testData.registration.step3_preferences.ui.defaults["TS-311"].testData;
        await registerPage.verifyPrivacyDefaults({
          isProfilePrivate: defaults.isProfilePrivate,
          allowMessages: defaults.allowMessages,
          emailNotifications: defaults.emailNotifications
        });
      });
    });

    test('TS-312: Configure profile privacy setting', async () => {
      await test.step('Toggle profile privacy', async () => {
        const privacyData = testData.registration.step3_preferences.ui.profilePrivacy["TS-312"].testData;
        await registerPage.setProfilePrivacy(privacyData.makePrivate);
        await registerPage.verifyProfilePrivacy(privacyData.expectedToggleState);

        await registerPage.setProfilePrivacy(!privacyData.makePrivate);
        await registerPage.verifyProfilePrivacy(!privacyData.expectedToggleState);
      });
    });

    test('TS-313: Configure message reception setting', async () => {
      await test.step('Toggle message reception', async () => {
        const messageData = testData.registration.step3_preferences.ui.messagePreferences["TS-313"].testData;
        await registerPage.setMessageReception(messageData.allowMessages);
        await registerPage.verifyMessageReception(messageData.expectedToggleState);

        await registerPage.setMessageReception(!messageData.allowMessages);
        await registerPage.verifyMessageReception(!messageData.expectedToggleState);
      });
    });

    test('TS-314: Configure email notification setting', async () => {
      await test.step('Toggle email notifications', async () => {
        const notificationData = testData.registration.step3_preferences.ui.notificationPreferences["TS-314"].testData;
        await registerPage.setEmailNotifications(notificationData.enableNotifications);
        await registerPage.verifyEmailNotifications(notificationData.expectedToggleState);

        await registerPage.setEmailNotifications(!notificationData.enableNotifications);
        await registerPage.verifyEmailNotifications(!notificationData.expectedToggleState);
      });
    });
  });

  // Valid Submission Test
  test('TS-320: Verify successful submission with privacy settings', async ({ page }) => {
    const validData = testData.registration.api.validMentor;

    await test.step('Submit complete registration form', async () => {
      // Set privacy settings
      await registerPage.setProfilePrivacy(validData.IsPrivateProfile);
      await registerPage.setMessageReception(validData.IsReceiveMessage);
      await registerPage.setEmailNotifications(validData.IsNotification);

      // Submit final step
      await registerPage.clickCreateAccount();

      // Verify successful registration through UI
      await expect(page.getByText('Account created successfully')).toBeVisible();
    });
  });
});