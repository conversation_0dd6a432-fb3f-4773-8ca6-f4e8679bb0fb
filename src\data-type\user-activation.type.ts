export interface UserActivationData {
    userIndex: number;
    action: 'activate' | 'deactivate';
    expectedMessage: string;
}

export interface AdminCredentials {
    email: string;
    password: string;
}

export interface UserActivationTestData {
    adminCredentials: AdminCredentials;
    activationScenarios: UserActivationData[];
}

export interface ActivationDialog {
    dialogName: string;
    buttonName: string;
    successMessage: string;
}
