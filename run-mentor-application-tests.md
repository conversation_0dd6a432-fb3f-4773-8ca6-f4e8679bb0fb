# Mentor Application Tracking Tests

## Overview
This implementation provides a comprehensive test suite for mentor application tracking functionality using the Page Object Model (POM) pattern.

## Files Created

### 1. Data Types (`src/data-type/mentor-application.type.ts`)
- Defines TypeScript interfaces for mentor application data
- Includes `ApplicationStatus`, `MentorApplicationData`, `PresubmittedMentorAccount`, and `MentorApplicationTestData` types

### 2. Test Data (`src/tests-data/mentor-application-data.json`)
- Contains presubmitted mentor account credentials
- Includes test data for the refresh functionality test
- Provides multiple mentor accounts for data-driven testing

### 3. Page Object (`src/pages/mentor-application/mentor-application-status-page.ts`)
- Implements POM pattern for the Mentor Application Status page
- Provides methods for navigation, verification, and interaction
- Includes locators for all page elements

### 4. Test Implementation (`src/tests/ui/mentor-application/mentor-application-tracking.spec.ts`)
- Main test file implementing the refresh test scenario
- Includes data-driven tests for multiple mentor accounts
- Follows the exact steps specified in the requirements

## Test Scenarios

### Main Refresh Test
1. **Step 1**: Navigate to home page
2. **Step 2**: Go to login page  
3. **Step 3**: <PERSON><PERSON> as presubmitted application mentor from test-data
4. **Step 4**: Verify on Mentor Application Status page
5. **Step 5**: Click refresh button
6. **Check Result**: Verify fullname and email match account from test-data

### Data-Driven Tests
- Tests multiple presubmitted mentor accounts
- Verifies application status for each mentor
- Ensures consistent behavior across different mentor profiles

## Running the Tests

### Run specific test file:
```bash
npx playwright test src/tests/ui/mentor-application/mentor-application-tracking.spec.ts
```

### Run with specific tag:
```bash
npx playwright test --grep "@MentorApplicationTracking"
```

### Run in headed mode (to see browser):
```bash
npx playwright test src/tests/ui/mentor-application/mentor-application-tracking.spec.ts --headed
```

### Run with debug mode:
```bash
npx playwright test src/tests/ui/mentor-application/mentor-application-tracking.spec.ts --debug
```

## Test Data Structure

The test uses the following mentor account for the refresh test:
- **Email**: <EMAIL>
- **Password**: refresh123A@
- **Full Name**: mentor123
- **Expected Status**: Pending

## Key Features

1. **POM Pattern**: Clean separation of page logic and test logic
2. **Type Safety**: Full TypeScript support with proper interfaces
3. **Data-Driven**: Multiple test scenarios from JSON data
4. **Reusable**: Page objects can be used across multiple test files
5. **Maintainable**: Clear structure following existing codebase patterns

## Notes

- The implementation follows the existing codebase patterns and structure
- All locators use Playwright's recommended practices
- Test steps are clearly defined and documented
- Error handling and verification methods are included
- The code is ready for integration with existing CI/CD pipelines
