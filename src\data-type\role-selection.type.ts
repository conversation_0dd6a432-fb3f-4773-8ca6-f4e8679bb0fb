export interface RoleSelectionData {
    roleName: string;
    expectedRoleValue: string;
    description: string;
}

export interface AdminCredentials {
    email: string;
    password: string;
}

export interface RoleSelectionTestData {
    adminCredentials: AdminCredentials;
    roleSelectionScenarios: RoleSelectionData[];
}

export interface UserTableRow {
    rowIndex: number;
    expectedRole: string;
}
