import { Page, expect, Locator } from '@playwright/test';
import { BasePage } from '../base-page';

export class MentorApplicationStatusPage extends BasePage {
    // Page elements
    private readonly pageTitle: Locator;
    private readonly refreshButton: Locator;
    private readonly applicationStatusSection: Locator;
    private readonly fullNameHeading: Locator;
    private readonly emailText: Locator;
    private readonly statusBadge: Locator;

    constructor(page: Page) {
        super(page);
        
        // Initialize locators
        this.pageTitle = this.page.getByText('Mentor Application Status');
        this.refreshButton = this.page.getByRole('button', { name: 'Refresh' });
        this.applicationStatusSection = this.page.locator('[data-testid="application-status"]');
        this.fullNameHeading = this.page.getByRole('heading');
        this.emailText = this.page.locator('[data-testid="mentor-email"]');
        this.statusBadge = this.page.locator('[data-testid="application-status-badge"]');
    }

    // Navigation methods
    async navigateToMentorApplicationStatus(): Promise<void> {
        await this.page.goto('/mentor/application-status');
    }

    // Verification methods
    async verifyOnMentorApplicationStatusPage(): Promise<void> {
        await expect(this.pageTitle).toBeVisible();
        await expect(this.page).toHaveURL(/.*mentor.*application.*status.*/);
    }

    async verifyPageTitle(): Promise<void> {
        await expect(this.pageTitle).toBeVisible();
    }

    // Action methods
    async clickRefreshButton(): Promise<void> {
        await this.refreshButton.click();
    }

    // Verification methods for mentor information
    async verifyMentorFullName(expectedFullName: string): Promise<void> {
        const fullNameHeading = this.page.getByRole('heading', { name: expectedFullName });
        await expect(fullNameHeading).toBeVisible();
        await fullNameHeading.click(); // As specified in the requirements
    }

    async verifyMentorEmail(expectedEmail: string): Promise<void> {
        const emailElement = this.page.getByText(expectedEmail);
        await expect(emailElement).toBeVisible();
        await emailElement.click(); // As specified in the requirements
    }

    async verifyApplicationStatus(expectedStatus: string): Promise<void> {
        const statusElement = this.page.getByText(expectedStatus);
        await expect(statusElement).toBeVisible();
    }

    // Combined verification method
    async verifyMentorApplicationDetails(fullName: string, email: string): Promise<void> {
        await this.verifyMentorFullName(fullName);
        await this.verifyMentorEmail(email);
    }

    // Wait for page to load completely
    async waitForPageLoad(): Promise<void> {
        await this.page.waitForLoadState('networkidle');
        await expect(this.pageTitle).toBeVisible();
    }

    // Get current application status
    async getCurrentApplicationStatus(): Promise<string> {
        return await this.statusBadge.textContent() || '';
    }

    // Check if refresh button is enabled
    async isRefreshButtonEnabled(): Promise<boolean> {
        return await this.refreshButton.isEnabled();
    }
}
