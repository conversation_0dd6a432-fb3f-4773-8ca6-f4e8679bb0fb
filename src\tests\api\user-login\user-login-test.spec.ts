import { expect, test } from "@playwright/test";
import testData from "@tests-data/user-login-data.json";
import LoginLogoutAPI from "src/api/login-logout/login-logout.api";
require('dotenv').config();


test.describe('User Login API', () => {
    let loginAPI: LoginLogoutAPI

    test.beforeEach(async ({ request }) => {
        loginAPI = new LoginLogoutAPI(request);
    });

    test("Verify user login API sucessfully", async () => {
        let res: any;

        await test.step('Send POST request with valid credentials', async () => {
            res = await loginAPI.login(process.env.EMAIL, process.env.PASSWORD);
        });

        await test.step('Verify successful login', async () => {
            expect(res.status()).toBe(200);
        });
    });

    testData.userAccount.forEach((account, index) => {
        test(`Negative case ${index + 1}: Verify user login API`, async () => {
            let res: any;
            let resBody: any;

            await test.step('Send POST request with invalid credentials', async () => {
                res = await loginAPI.login(account.email, account.password);
                resBody = await res.json();
            });

            await test.step('Verify error response', async () => {
                expect(res.status()).toBe(account.status);
                expect(resBody.errors[0].message).toBe(account.message);
            });
        });
    })
})

