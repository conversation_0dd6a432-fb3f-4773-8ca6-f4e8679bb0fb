import { Locator, Page, expect } from '@playwright/test';
import { BasePage } from '../base-page';

export class MentorApplicationStatusPage extends BasePage {
    // Page elements
    private readonly mentorApplicationStatusHeading: Locator;
    private readonly refreshButton: Locator;
    private readonly mentorPlatformLink: Locator;

    constructor(page: Page) {
        super(page);
        this.mentorApplicationStatusHeading = this.page.getByText('Mentor Application Status');
        this.refreshButton = this.page.getByRole('button', { name: 'Refresh' });
        this.mentorPlatformLink = this.page.getByRole('link', { name: 'Mentor Platform' });
    }

    async navigateToMentorApplicationStatus(): Promise<void> {
        // Navigate to the mentor application status page
        await this.mentorApplicationStatusHeading.click();
    }

    async verifyOnMentorApplicationStatusPage(): Promise<void> {
        await expect(this.mentorApplicationStatusHeading).toBeVisible();
    }

    async clickRefreshButton(): Promise<void> {
        await this.refreshButton.click();
        // Wait for any loading/refresh to complete
        await this.page.waitForLoadState('networkidle');
    }

    async verifyMentorApplicationDetails(fullName: string, email: string): Promise<void> {
        // Verify fullname matches
        const nameHeading = this.page.getByRole('heading', { name: fullName });
        await expect(nameHeading).toBeVisible();
        await nameHeading.click();

        // Verify email matches
        const emailText = this.page.getByText(email);
        await expect(emailText).toBeVisible();
        await emailText.click();
    }

    async verifyApplicationStatus(expectedStatus: string): Promise<void> {
        const statusElement = this.page.getByText(expectedStatus);
        await expect(statusElement).toBeVisible();
    }

    async getMentorFullName(): Promise<string | null> {
        const nameHeading = this.page.getByRole('heading').first();
        return await nameHeading.textContent();
    }

    async getMentorEmail(): Promise<string | null> {
        // This assumes email is displayed as text on the page
        // You may need to adjust the selector based on actual page structure
        const emailElement = this.page.locator('[data-testid="mentor-email"]').or(
            this.page.locator('text=/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/')
        );
        return await emailElement.first().textContent();
    }
}
