import { test, expect } from '@playwright/test';
import { HomePage } from '../../../pages/register/home-page';
import { LoginPage } from '../../../pages/login/login-page';
import { UserManagementPage } from '../../../pages/user-management/user-management-page';
import testData from '../../../tests-data/user-search-data.json';
import { UserSearchTestData } from '../../../data-type/user-search.type';

test.describe('User Search Tests', () => {
    let homePage: HomePage;
    let loginPage: LoginPage;
    let userManagementPage: UserManagementPage;
    const searchData: UserSearchTestData = testData as UserSearchTestData;

    test.beforeEach(async ({ page }) => {
        homePage = new HomePage(page);
        loginPage = new LoginPage(page);
        userManagementPage = new UserManagementPage(page);
    });

    test('@UserSearch Verify user search functionality for <PERSON><PERSON>', async ({ page }) => {
        await test.step('Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Login with admin credentials', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                searchData.adminCredentials.email,
                searchData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyLoginSuccessfully();
        });

        await test.step('Navigate to Manage Users page', async () => {
            await userManagementPage.clickManageUsersLink();
            await userManagementPage.verifyOnUserManagementPage();
        });

        await test.step('Search for Tallia user', async () => {
            await userManagementPage.clickSearchBox();
            await userManagementPage.searchForUser('Tallia');
        });

        await test.step('Verify search results', async () => {
            await userManagementPage.clickNameColumnHeader();
            await userManagementPage.verifyUserInResults('Tallia Bursnell');
            await userManagementPage.clickOnUserName('Tallia Bursnell');
        });
    });

    // Data-driven test for multiple search scenarios
    searchData.searchScenarios.forEach((scenario, index) => {
        test(`@UserSearch Case ${index + 1}: Search for "${scenario.searchTerm}"`, async ({ page }) => {
            await test.step('Go to home page', async () => {
                await loginPage.goToBrowser();
            });

            await test.step('Go to login page', async () => {
                await loginPage.clickOnSignInLink();
            });

            await test.step('Login with admin credentials', async () => {
                await loginPage.enterEmailAndPasswordToTextBox(
                    searchData.adminCredentials.email,
                    searchData.adminCredentials.password
                );
                await loginPage.clickOnSignInButton();
                await loginPage.verifyLoginSuccessfully();
            });

            await test.step('Navigate to Manage Users page', async () => {
                await userManagementPage.clickManageUsersLink();
                await userManagementPage.verifyOnUserManagementPage();
            });

            await test.step(`Search for "${scenario.searchTerm}"`, async () => {
                await userManagementPage.clickSearchBox();
                await userManagementPage.searchForUser(scenario.searchTerm);
            });

            await test.step('Verify search results', async () => {
                if (scenario.expectedResults.length > 0) {
                    const expectedUserNames = scenario.expectedResults
                        .filter(result => result.shouldBeVisible)
                        .map(result => result.name);
                    
                    await userManagementPage.verifySearchResults(expectedUserNames);
                } else {
                    await userManagementPage.verifyNoResults();
                }
            });
        });
    });
});
