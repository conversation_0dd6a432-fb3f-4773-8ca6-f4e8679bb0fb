// Mentor application status types
export type ApplicationStatus = 'Pending' | 'Approved' | 'Rejected' | 'Under Review';

// Mentor application data interface
export interface MentorApplicationData {
    fullName: string;
    email: string;
    status: ApplicationStatus;
    submissionDate?: string;
    lastUpdated?: string;
}

// Presubmitted mentor account credentials
export interface PresubmittedMentorAccount {
    email: string;
    password: string;
    fullName: string;
    expectedStatus: ApplicationStatus;
}

// Test data structure for mentor application tracking
export interface MentorApplicationTestData {
    presubmittedMentors: PresubmittedMentorAccount[];
    refreshTestMentor: PresubmittedMentorAccount;
}
