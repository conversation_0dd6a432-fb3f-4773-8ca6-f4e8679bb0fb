import { test, expect } from '@playwright/test';
import RegisterAPI from '../../../api/register/register.api';
import userData from '../../../tests-data/user-register-data.json';
import apiTestData from '../../../tests-data/user-register-api-data.json';
import { RegisterUserRequest } from '../../../data-type/register-user.type';
import { mapToRegisterRequest } from '../../../data-type/register-user-mapper.type';

test.describe('User Registration Tests', () => {
    test('SYP_036 - Verify successful account creation with valid required fields', async ({ request }) => {
        let registerAPI: RegisterAPI;
        let testData: RegisterUserRequest;
        let response: any;
        let responseText: string;
        let responseBody: any;

        await test.step('Arrange test data and API instance', async () => {
            registerAPI = new RegisterAPI(request);
            testData = userData.registration.api.validMentor as RegisterUserRequest;
        });

        await test.step('Send registration request with valid data', async () => {
            response = await registerAPI.register(testData);
            responseText = await response.text();
            responseBody = JSON.parse(responseText);
        });

        await test.step('Verify successful registration response', async () => {
            expect(response.ok()).toBeTruthy();
            expect(response.status()).toBe(200);
            expect(responseBody.data).toBe('Registered account successfully.');
            expect(responseBody.isSuccess).toBeTruthy();
            expect(responseBody.statusCode).toBe(200);
            expect(responseBody.errors).toHaveLength(0);
        });
    });

    test('SYP_037 - Verify error when registering with duplicate email', async ({ request }) => {
        let registerAPI: RegisterAPI;
        let testData: RegisterUserRequest;
        let response: any;
        let responseText: string;

        await test.step('Arrange test data with duplicate email', async () => {
            registerAPI = new RegisterAPI(request);
            testData = mapToRegisterRequest(apiTestData.unsuccessRegister[0]);
        });

        await test.step('Send registration request with duplicate email', async () => {
            response = await registerAPI.register(testData);
            responseText = await response.text();
        });

        await test.step('Verify duplicate email error response', async () => {
            expect(response.ok()).toBeFalsy();
            expect(response.status()).toBe(400);
            expect(responseText).toBe('Email already register.');
        });
    });

    test('SYP_038 - Verify error when registering with invalid expertise', async ({ request }) => {
        let registerAPI: RegisterAPI;
        let testData: RegisterUserRequest;
        let response: any;
        let responseText: string;

        await test.step('Arrange test data with invalid expertise', async () => {
            registerAPI = new RegisterAPI(request);
            testData = mapToRegisterRequest(apiTestData.unsuccessRegister[1]);
        });

        await test.step('Send registration request with invalid expertise', async () => {
            response = await registerAPI.register(testData);
            responseText = await response.text();
        });

        await test.step('Verify invalid expertise error response', async () => {
            expect(response.ok()).toBeFalsy();
            expect(response.status()).toBe(400);
            expect(responseText).toBe('Areas of expertise have invalid value.');
        });
    });

    test('SYP_039 - Verify error when registering with invalid course category', async ({ request }) => {
        let registerAPI: RegisterAPI;
        let testData: RegisterUserRequest;
        let response: any;
        let responseText: string;

        await test.step('Arrange test data with invalid course category', async () => {
            registerAPI = new RegisterAPI(request);
            testData = mapToRegisterRequest(apiTestData.unsuccessRegister[2]);
        });

        await test.step('Send registration request with invalid course category', async () => {
            response = await registerAPI.register(testData);
            responseText = await response.text();
        });

        await test.step('Verify invalid course category error response', async () => {
            expect(response.ok()).toBeFalsy();
            expect(response.status()).toBe(400);
            expect(responseText).toBe('Topics have invalid value.');
        });
    });
    test('SYP_040 - Verify validation flow for user registration', async ({ request }) => {
        let registerAPI: RegisterAPI;
        let validData: RegisterUserRequest;
        let response: any;
        let responseText: string;

        await test.step('Arrange test data for validation flow', async () => {
            registerAPI = new RegisterAPI(request);
            validData = mapToRegisterRequest(apiTestData.successRegister);
        });

        await test.step('Send registration request with valid data', async () => {
            response = await registerAPI.register(validData);
            responseText = await response.text();
        });

        await test.step('Verify successful registration with validation flow', async () => {
            expect(response.ok()).toBeTruthy();
            expect(response.status()).toBe(200);
            try {
                const responseBody = JSON.parse(responseText);
                expect(responseBody.isSuccess).toBeTruthy();
                expect(responseBody.data).toBe('Registered account successfully.');
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.errors).toHaveLength(0);
            } catch (e) {
                expect(responseText).toContain('Registered account successfully');
            }
        });
    });
});
