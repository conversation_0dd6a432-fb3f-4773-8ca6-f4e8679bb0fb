import { test, expect } from '@playwright/test';
import { HomePage } from '../../../pages/register/home-page';
import { LoginPage } from '../../../pages/login/login-page';
import { MentorApprovalsPage } from '../../../pages/mentor-approvals/mentor-approvals-page';
import { MentorDetailPage } from '../../../pages/mentor-approvals/mentor-detail-page';
import testData from '../../../tests-data/mentor-approvals-data.json';
import { MentorApprovalsTestData, MentorReviewTestData } from '../../../data-type/mentor-approvals.type';

test.describe('Mentor Review Functions Tests', () => {
    let homePage: HomePage;
    let loginPage: LoginPage;
    let mentorApprovalsPage: MentorApprovalsPage;
    let mentorDetailPage: MentorDetailPage;
    const reviewData: MentorApprovalsTestData = testData as MentorApprovalsTestData;

    test.beforeEach(async ({ page }) => {
        homePage = new HomePage(page);
        loginPage = new LoginPage(page);
        mentorApprovalsPage = new MentorApprovalsPage(page);
        mentorDetailPage = new MentorDetailPage(page);
    });

    test('@MentorReview Review Functions test - View detailed application with all sections', async ({ page }) => {
        // Use the first review scenario from test data
        const scenario: MentorReviewTestData = reviewData.reviewScenarios![0];

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Login with admin credentials (user must be admin)', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                reviewData.adminCredentials.email,
                reviewData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyAdminLoginSuccessfully();
        });

        await test.step('Step 4: Click Mentor Approvals link', async () => {
            await mentorApprovalsPage.clickMentorApprovalsLink();
        });

        await test.step('Make sure it\'s on Mentor Approvals Management page', async () => {
            await mentorApprovalsPage.verifyOnMentorApprovalsPage();
            await mentorApprovalsPage.clickMentorApprovalsHeading();
        });

        await test.step(`Step 5: Search for "${scenario.searchTerm}" from test-data`, async () => {
            // Click search box
            await mentorApprovalsPage.clickSearchBox();

            // Fill search term from test-data
            await mentorApprovalsPage.searchForMentor(scenario.searchTerm);

            // Check search results
            await mentorApprovalsPage.clickFullNameColumnHeader();
            await mentorApprovalsPage.verifyMentorInResults(scenario.mentorDetails.fullName);
            await mentorApprovalsPage.clickOnMentorName(scenario.mentorDetails.fullName);
        });

        await test.step('Step 6: Click view details application button', async () => {
            await mentorApprovalsPage.clickViewDetailsButton(scenario.mentorDetails.fullName);
        });

        await test.step('Step 7: Verify mentor name heading and all application sections', async () => {
            // Check mentor name heading first
            await mentorDetailPage.verifyMentorNameHeading(scenario.mentorDetails.fullName);

            // Then check all required sections based on test data
            if (scenario.mentorDetails.hasApplicantInformation) {
                await mentorDetailPage.verifyApplicantInformationSection();
            }

            if (scenario.mentorDetails.hasApplicationDecision) {
                await mentorDetailPage.verifyApplicationDecisionSection();
            }

            if (scenario.mentorDetails.hasEducation) {
                await mentorDetailPage.verifyEducationSection();
            }

            if (scenario.mentorDetails.hasWorkExperience) {
                await mentorDetailPage.verifyWorkExperienceSection();
            }

            if (scenario.mentorDetails.hasCertifications) {
                await mentorDetailPage.verifyCertificationsSection();
            }

            if (scenario.mentorDetails.hasMotivation) {
                await mentorDetailPage.verifyMotivationSection();
            }

            if (scenario.mentorDetails.hasSupportingDocuments) {
                await mentorDetailPage.verifySupportingDocumentsSection();
            }
        });

        await test.step('Verify complete detailed application view', async () => {
            // Comprehensive verification of the entire application detail view
            await mentorDetailPage.verifyDetailedApplicationView(scenario.mentorDetails.fullName);
        });

        await test.step('Close the detail view', async () => {
            // Close the mentor detail modal/page
            await mentorDetailPage.clickCloseButton();
        });
    });

    // Additional test for multiple review scenarios if more data is added
    test('@MentorReview Review Functions test - Multiple mentor applications review', async ({ page }) => {
       

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Login with admin credentials (user must be admin)', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                reviewData.adminCredentials.email,
                reviewData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyAdminLoginSuccessfully();
        });

        await test.step('Step 4: Click Mentor Approvals link', async () => {
            await mentorApprovalsPage.clickMentorApprovalsLink();
        });

        await test.step('Make sure it\'s on Mentor Approvals Management page', async () => {
            await mentorApprovalsPage.verifyOnMentorApprovalsPage();
            await mentorApprovalsPage.clickMentorApprovalsHeading();
        });

        // Loop through all review scenarios from test-data
        for (const scenario of reviewData.reviewScenarios!) {
            await test.step(`Review application for "${scenario.mentorDetails.fullName}"`, async () => {
                // Search for mentor
                await mentorApprovalsPage.clickSearchBox();
                await mentorApprovalsPage.searchForMentor(scenario.searchTerm);

                // Verify search results
                await mentorApprovalsPage.clickFullNameColumnHeader();
                await mentorApprovalsPage.verifyMentorInResults(scenario.mentorDetails.fullName);
                await mentorApprovalsPage.clickOnMentorName(scenario.mentorDetails.fullName);

                // Click view details
                await mentorApprovalsPage.clickViewDetailsButton(scenario.mentorDetails.fullName);

                // Verify detailed application view
                await mentorDetailPage.verifyDetailedApplicationView(scenario.mentorDetails.fullName);

                // Close the detail view before navigating back
                await mentorDetailPage.clickCloseButton();

                // Navigate back to mentor approvals list for next iteration
                await mentorApprovalsPage.clickMentorApprovalsLink();
                await mentorApprovalsPage.verifyOnMentorApprovalsPage();
            });
        }
    });
});
