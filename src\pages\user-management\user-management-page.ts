import { Locator, Page, expect } from '@playwright/test';
import { BasePage } from '../base-page';

export class UserManagementPage extends BasePage {
    // Locators
    private readonly manageUsersLink: Locator;
    private readonly searchBox: Locator;
    private readonly nameColumnHeader: Locator;
    private readonly roleColumnHeader: Locator;
    private readonly userTable: Locator;
    private readonly selectRolesButton: Locator;

    constructor(page: Page) {
        super(page);
        this.manageUsersLink = this.page.getByRole('link', { name: 'Manage Users' });
        this.searchBox = this.page.getByRole('textbox', { name: 'Search...' });
        this.nameColumnHeader = this.page.getByRole('cell', { name: 'Name' });
        this.roleColumnHeader = this.page.getByRole('cell', { name: 'Role' });
        this.userTable = this.page.locator('table, [role="table"]');
        this.selectRolesButton = this.page.getByRole('button', { name: 'Select roles' });
    }

    async clickManageUsersLink(): Promise<void> {
        await this.manageUsersLink.click();
    }

    async verifyOnUserManagementPage(): Promise<void> {
        await expect(this.manageUsersLink).toBeVisible();
        await expect(this.page).toHaveURL(/.*user.*management.*|.*manage.*user.*/i);
    }

    async clickSearchBox(): Promise<void> {
        await this.searchBox.click();
    }

    async searchForUser(searchTerm: string): Promise<void> {
        await this.searchBox.fill(searchTerm);
    }

    async clickNameColumnHeader(): Promise<void> {
        await this.nameColumnHeader.click();
    }

    async verifyUserInResults(userName: string): Promise<void> {
        const userNameLocator = this.page.getByText(userName);
        await expect(userNameLocator).toBeVisible();
    }

    async clickOnUserName(userName: string): Promise<void> {
        await this.page.getByText(userName).click();
    }

    async verifySearchResults(expectedUsers: string[]): Promise<void> {
        for (const userName of expectedUsers) {
            await this.verifyUserInResults(userName);
        }
    }

    async verifyNoResults(): Promise<void> {
        // Check if table is empty or shows "No results" message
        const noResultsMessage = this.page.getByText(/no.*results|no.*users.*found|empty/i);
        const emptyTable = this.userTable.locator('tbody tr').count();

        try {
            await expect(noResultsMessage).toBeVisible({ timeout: 5000 });
        } catch {
            // If no "no results" message, check if table is empty
            expect(await emptyTable).toBe(0);
        }
    }

    // Role Selection Methods
    async clickSelectRolesButton(): Promise<void> {
        await this.selectRolesButton.click();
    }

    async selectRoleOption(roleName: string): Promise<void> {
        const roleOption = this.page.getByRole('option', { name: roleName });
        await roleOption.click();
    }

    async clickRoleColumnHeader(): Promise<void> {
        await this.roleColumnHeader.click();
    }

    async verifyUserRoleInRow(rowIndex: number, expectedRole: string): Promise<void> {
        const roleCell = this.page.locator(`tr:nth-child(${rowIndex + 1}) > td:nth-child(3)`);
        await roleCell.click();
        await expect(roleCell).toContainText(expectedRole);
    }

    async verifyAllUsersHaveRole(rowIndices: number[], expectedRole: string): Promise<void> {
        for (const rowIndex of rowIndices) {
            await this.verifyUserRoleInRow(rowIndex, expectedRole);
        }
    }

    async clickRoleCellInRow(rowIndex: number): Promise<void> {
        const roleCell = this.page.locator(`tr:nth-child(${rowIndex + 1}) > td:nth-child(3)`);
        await roleCell.click();
    }

    async getRoleTextFromRow(rowIndex: number): Promise<string> {
        const roleCell = this.page.locator(`tr:nth-child(${rowIndex + 1}) > td:nth-child(3)`);
        return await roleCell.textContent() || '';
    }

    async getAvailableRowCount(): Promise<number> {
        const rows = this.userTable.locator('tbody tr');
        return await rows.count();
    }

    async verifyRoleInAvailableRows(expectedRole: string, maxRows: number = 10): Promise<void> {
        const availableRows = await this.getAvailableRowCount();
        const rowsToCheck = Math.min(availableRows, maxRows);

        for (let i = 1; i <= rowsToCheck; i++) {
            const roleCell = this.page.locator(`tr:nth-child(${i + 1}) > td:nth-child(3)`);
            if (await roleCell.isVisible()) {
                await roleCell.click();
                await expect(roleCell).toContainText(expectedRole);
            }
        }
    }

    async clickAvailableRoleCells(maxRows: number = 10): Promise<void> {
        const availableRows = await this.getAvailableRowCount();
        const rowsToCheck = Math.min(availableRows, maxRows);

        for (let i = 1; i <= rowsToCheck; i++) {
            const roleCell = this.page.locator(`tr:nth-child(${i + 1}) > td:nth-child(3)`);
            if (await roleCell.isVisible()) {
                await roleCell.click();
            }
        }
    }

    async clickAllTableRoleCells(): Promise<void> {
        // Wait for table to load
        await this.userTable.waitFor({ state: 'visible' });

        // Get all role cells (3rd column) in the table body
        const roleCells = this.userTable.locator('tbody tr td:nth-child(3)');
        const cellCount = await roleCells.count();

        console.log(`Found ${cellCount} role cells to click`);

        // Click each role cell
        for (let i = 0; i < cellCount; i++) {
            const roleCell = roleCells.nth(i);
            if (await roleCell.isVisible()) {
                await roleCell.click();
                console.log(`Clicked role cell ${i + 1}`);
            }
        }
    }

    async verifyAllTableRowsHaveRole(expectedRole: string): Promise<void> {
        // Wait for table to load
        await this.userTable.waitFor({ state: 'visible' });

        // Get all role cells (3rd column) in the table body
        const roleCells = this.userTable.locator('tbody tr td:nth-child(3)');
        const cellCount = await roleCells.count();

        console.log(`Verifying ${cellCount} role cells have role: ${expectedRole}`);

        // Verify each role cell contains the expected role
        for (let i = 0; i < cellCount; i++) {
            const roleCell = roleCells.nth(i);
            if (await roleCell.isVisible()) {
                const roleText = await roleCell.textContent();
                console.log(`Row ${i + 1} role: ${roleText}`);
                await expect(roleCell).toContainText(expectedRole);
            }
        }
    }

    async waitForFilteredResults(): Promise<void> {
        // Wait a moment for the filter to be applied
        await this.page.waitForTimeout(2000);
        await this.userTable.waitFor({ state: 'visible' });
    }

    async clearRoleFilter(): Promise<void> {
        // Try to clear any existing role filter by clicking the select roles button again
        // and potentially clicking away or pressing escape
        try {
            await this.selectRolesButton.click();
            await this.page.keyboard.press('Escape');
            await this.page.waitForTimeout(1000);
        } catch (error) {
            console.log('Could not clear role filter:', error);
        }
    }

    async refreshPage(): Promise<void> {
        await this.page.reload();
        await this.page.waitForLoadState('networkidle');
        await this.userTable.waitFor({ state: 'visible' });
    }

    async navigateToUserManagement(): Promise<void> {
        await this.clickManageUsersLink();
        await this.verifyOnUserManagementPage();
        await this.page.waitForLoadState('networkidle');
    }

    // User Activation/Deactivation Methods
    async clickDeactivateUserButton(userIndex: number): Promise<void> {
        const deactivateButton = this.page.getByRole('button', { name: 'Deactivate user' }).nth(userIndex);
        await deactivateButton.click();
    }

    async clickActivateUserButton(userIndex: number): Promise<void> {
        const activateButton = this.page.getByRole('button', { name: 'Activate user', exact: true }).nth(userIndex);
        await activateButton.click();
    }

    async verifyDeactivateDialog(): Promise<void> {
        const dialog = this.page.getByRole('dialog', { name: 'Deactivate Account' });
        await expect(dialog).toBeVisible();
        await dialog.click();
    }

    async verifyActivateDialog(): Promise<void> {
        const dialog = this.page.getByRole('dialog', { name: 'Activate Account' });
        await expect(dialog).toBeVisible();
        await dialog.click();
    }

    async clickDeactivateConfirmButton(): Promise<void> {
        const confirmButton = this.page.getByRole('button', { name: 'Deactivate' });
        await confirmButton.click();
    }

    async clickActivateConfirmButton(): Promise<void> {
        const confirmButton = this.page.getByRole('button', { name: 'Activate' });
        await confirmButton.click();
    }

    async verifyDeactivationMessage(): Promise<void> {
        const message = this.page.getByText('Deactivated user.');
        await expect(message).toBeVisible();
        await message.click();
    }

    async verifyActivationMessage(): Promise<void> {
        const message = this.page.getByText('Activated user.');
        await expect(message).toBeVisible();
        await message.click();
    }

    async deactivateUser(userIndex: number): Promise<void> {
        await this.clickDeactivateUserButton(userIndex);
        await this.verifyDeactivateDialog();
        await this.clickDeactivateConfirmButton();
        await this.verifyDeactivationMessage();
    }

    async activateUser(userIndex: number): Promise<void> {
        await this.clickActivateUserButton(userIndex);
        await this.verifyActivateDialog();
        await this.clickActivateConfirmButton();
        await this.verifyActivationMessage();
    }

    async performUserAction(action: 'activate' | 'deactivate', userIndex: number): Promise<void> {
        if (action === 'deactivate') {
            await this.deactivateUser(userIndex);
        } else {
            await this.activateUser(userIndex);
        }
    }
}
